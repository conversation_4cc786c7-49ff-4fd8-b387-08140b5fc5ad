import Phaser from 'phaser';
import Enemy from './Enemy';

// Syntax Error Enemy - Fast moving, represents syntax mistakes
export class SyntaxErrorEnemy extends Enemy {
  private errorMessages: string[] = [
    'Missing semicolon!',
    'Unclosed bracket!',
    'Typo detected!',
    'Invalid syntax!'
  ];

  constructor(scene: Phaser.Scene, x: number, y: number, enemyData: any) {
    super(scene, x, y, {
      ...enemyData,
      speed: enemyData.speed * 1.5, // 50% faster
      health: enemyData.health * 0.8 // 20% less health
    });
    
    // Add blinking effect to represent errors
    this.createBlinkingEffect();
  }

  private createBlinkingEffect() {
    this.scene.tweens.add({
      targets: this,
      alpha: 0.5,
      duration: 500,
      yoyo: true,
      repeat: -1,
      ease: 'Sine.easeInOut'
    });
  }

  protected createAttackEffect() {
    super.createAttackEffect();
    
    // Show random error message
    const message = this.errorMessages[Math.floor(Math.random() * this.errorMessages.length)];
    const errorText = this.scene.add.text(this.x, this.y - 40, message, {
      fontSize: '12px',
      color: '#ff0000',
      backgroundColor: '#000000',
      padding: { x: 5, y: 2 }
    }).setOrigin(0.5);
    
    this.scene.tweens.add({
      targets: errorText,
      y: errorText.y - 30,
      alpha: 0,
      duration: 1500,
      onComplete: () => errorText.destroy()
    });
  }
}

// Infinite Loop Enemy - Spins in place, hard to defeat
export class InfiniteLoopEnemy extends Enemy {
  private spinSpeed: number = 0.05;

  constructor(scene: Phaser.Scene, x: number, y: number, enemyData: any) {
    super(scene, x, y, {
      ...enemyData,
      behavior: 'static',
      health: enemyData.health * 2, // Double health
      speed: 0 // Doesn't move
    });
    
    this.setTint(0x9932cc); // Purple color for infinite loops
  }

  update() {
    super.update();
    
    // Constantly spin
    this.rotation += this.spinSpeed;
    
    // Pulse effect
    const pulse = Math.sin(this.scene.time.now * 0.01) * 0.2 + 1;
    this.setScale(pulse);
  }

  protected createAttackEffect() {
    // Create spinning attack effect
    const spinAttack = this.scene.add.graphics();
    spinAttack.lineStyle(3, 0x9932cc);
    spinAttack.strokeCircle(this.x, this.y, 50);
    
    this.scene.tweens.add({
      targets: spinAttack,
      scaleX: 2,
      scaleY: 2,
      alpha: 0,
      duration: 500,
      onComplete: () => spinAttack.destroy()
    });
    
    // Show "INFINITE LOOP" text
    const loopText = this.scene.add.text(this.x, this.y - 60, 'INFINITE LOOP!', {
      fontSize: '14px',
      color: '#9932cc',
      fontStyle: 'bold'
    }).setOrigin(0.5);
    
    this.scene.tweens.add({
      targets: loopText,
      y: loopText.y - 40,
      alpha: 0,
      duration: 2000,
      onComplete: () => loopText.destroy()
    });
  }
}

// Memory Leak Enemy - Grows larger over time
export class MemoryLeakEnemy extends Enemy {
  private growthRate: number = 0.001;
  private maxScale: number = 2;

  constructor(scene: Phaser.Scene, x: number, y: number, enemyData: any) {
    super(scene, x, y, {
      ...enemyData,
      behavior: 'patrol',
      speed: enemyData.speed * 0.7 // Slower movement
    });
    
    this.setTint(0xff4500); // Orange-red for memory issues
  }

  update() {
    super.update();
    
    // Gradually grow larger
    if (this.scaleX < this.maxScale) {
      this.setScale(this.scaleX + this.growthRate);
      
      // Increase health as it grows
      this.maxHealth += 0.1;
      this.health += 0.1;
    }
  }

  takeDamage(amount: number) {
    super.takeDamage(amount);
    
    // Shrink when damaged (memory freed)
    this.setScale(Math.max(0.5, this.scaleX - 0.1));
    this.maxHealth = Math.max(this.enemyData.health, this.maxHealth - 5);
  }

  protected createAttackEffect() {
    super.createAttackEffect();
    
    // Create expanding memory leak effect
    const memoryEffect = this.scene.add.graphics();
    memoryEffect.fillStyle(0xff4500, 0.3);
    memoryEffect.fillCircle(this.x, this.y, 30);
    
    this.scene.tweens.add({
      targets: memoryEffect,
      scaleX: 3,
      scaleY: 3,
      alpha: 0,
      duration: 1000,
      onComplete: () => memoryEffect.destroy()
    });
  }
}

// Null Pointer Enemy - Teleports randomly
export class NullPointerEnemy extends Enemy {
  private teleportCooldown: number = 0;
  private teleportRange: number = 150;

  constructor(scene: Phaser.Scene, x: number, y: number, enemyData: any) {
    super(scene, x, y, {
      ...enemyData,
      behavior: 'chase',
      speed: enemyData.speed * 0.5 // Slower but teleports
    });
    
    this.setTint(0x696969); // Gray for null/undefined
  }

  update() {
    super.update();
    
    // Update teleport cooldown
    if (this.teleportCooldown > 0) {
      this.teleportCooldown -= this.scene.game.loop.delta;
    }
    
    // Random teleportation
    if (this.teleportCooldown <= 0 && Math.random() < 0.002) {
      this.teleportRandomly();
    }
  }

  private teleportRandomly() {
    this.teleportCooldown = 3000; // 3 second cooldown
    
    // Fade out effect
    this.scene.tweens.add({
      targets: this,
      alpha: 0,
      duration: 200,
      onComplete: () => {
        // Teleport to new position
        const newX = this.x + Phaser.Math.Between(-this.teleportRange, this.teleportRange);
        const newY = this.y + Phaser.Math.Between(-50, 50);
        
        // Keep within bounds
        this.setPosition(
          Phaser.Math.Clamp(newX, 50, 750),
          Phaser.Math.Clamp(newY, 100, 500)
        );
        
        // Fade in effect
        this.scene.tweens.add({
          targets: this,
          alpha: 1,
          duration: 200
        });
        
        // Show "NULL!" text
        const nullText = this.scene.add.text(this.x, this.y - 30, 'NULL!', {
          fontSize: '12px',
          color: '#696969',
          fontStyle: 'bold'
        }).setOrigin(0.5);
        
        this.scene.tweens.add({
          targets: nullText,
          y: nullText.y - 20,
          alpha: 0,
          duration: 1000,
          onComplete: () => nullText.destroy()
        });
      }
    });
  }

  protected createAttackEffect() {
    super.createAttackEffect();
    
    // Create null pointer exception effect
    const nullEffect = this.scene.add.text(this.x, this.y - 50, 'NullPointerException!', {
      fontSize: '10px',
      color: '#696969',
      backgroundColor: '#ffffff',
      padding: { x: 3, y: 1 }
    }).setOrigin(0.5);
    
    this.scene.tweens.add({
      targets: nullEffect,
      y: nullEffect.y - 30,
      alpha: 0,
      duration: 1500,
      onComplete: () => nullEffect.destroy()
    });
  }
}
