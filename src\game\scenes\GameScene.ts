import Phaser from "phaser";
import { PlayerProgress } from "../../types/GameTypes";
import Player from "../objects/Player";
import Enemy from "../objects/Enemy";
import Collectible from "../objects/Collectible";

interface GameCallbacks {
  onPause: () => void;
  onGameOver: () => void;
  playerProgress: PlayerProgress;
  setPlayerProgress: React.Dispatch<React.SetStateAction<PlayerProgress>>;
}

export default class GameScene extends Phaser.Scene {
  private player!: Player;
  private platforms!: Phaser.Physics.Arcade.StaticGroup;
  private enemies!: Phaser.Physics.Arcade.Group;
  private collectibles!: Phaser.Physics.Arcade.Group;
  private cursors!: Phaser.Types.Input.Keyboard.CursorKeys;
  private wasdKeys!: any;
  private callbacks: GameCallbacks;
  private currentLevel: any;
  private gameData: any = {};

  constructor(callbacks: GameCallbacks) {
    super({ key: "GameScene" });
    this.callbacks = callbacks;
  }

  create() {
    // Load game data
    this.gameData.skills = this.cache.json.get("skills");
    this.gameData.levels = this.cache.json.get("levels");
    this.gameData.achievements = this.cache.json.get("achievements");
    this.gameData.config = this.cache.json.get("gameConfig");

    // Set up the current level (start with level 1)
    this.currentLevel = this.gameData.levels.levels[0];

    // Create the game world
    this.createWorld();
    this.createPlayer();
    this.createPlatforms();
    this.createEnemies();
    this.createCollectibles();

    // Set up input
    this.setupInput();

    // Set up physics collisions
    this.setupCollisions();

    // Set up camera
    this.cameras.main.setBounds(0, 0, 800, 600);
    this.cameras.main.startFollow(this.player);
  }

  update() {
    if (this.player) {
      this.player.update();
    }

    // Handle input
    this.handleInput();

    // Check for level completion
    this.checkLevelCompletion();
  }

  private createWorld() {
    // Add background
    this.add.rectangle(400, 300, 800, 600, 0x87ceeb);

    // Add some simple background elements
    this.add.rectangle(100, 500, 200, 200, 0x228b22); // Tree
    this.add.rectangle(700, 480, 150, 240, 0x8b4513); // Tree trunk
    this.add.circle(700, 380, 80, 0x228b22); // Tree top
  }

  private createPlayer() {
    this.player = new Player(this, 100, 400);
    this.add.existing(this.player);
    this.physics.add.existing(this.player);
  }

  private createPlatforms() {
    this.platforms = this.physics.add.staticGroup();

    // Create platforms from level data
    this.currentLevel.platforms.forEach((platformData: any) => {
      const platform = this.add.rectangle(
        platformData.x + platformData.width / 2,
        platformData.y + platformData.height / 2,
        platformData.width,
        platformData.height,
        0x8b4513
      );
      this.platforms.add(platform);
    });
  }

  private createEnemies() {
    this.enemies = this.physics.add.group();

    // Create enemies from level data
    this.currentLevel.enemies.forEach((enemyData: any) => {
      const enemy = new Enemy(this, enemyData.x, enemyData.y, enemyData);
      this.enemies.add(enemy);
    });
  }

  private createCollectibles() {
    this.collectibles = this.physics.add.group();

    // Create collectibles from level data
    this.currentLevel.collectibles.forEach((collectibleData: any) => {
      const collectible = new Collectible(
        this,
        collectibleData.x,
        collectibleData.y,
        collectibleData
      );
      this.collectibles.add(collectible);
    });
  }

  private setupInput() {
    this.cursors = this.input.keyboard!.createCursorKeys();
    this.wasdKeys = this.input.keyboard!.addKeys("W,S,A,D,SPACE");

    // Pause key
    this.input.keyboard!.on("keydown-ESC", () => {
      this.callbacks.onPause();
    });
  }

  private setupCollisions() {
    // Player collisions
    this.physics.add.collider(this.player, this.platforms);
    this.physics.add.collider(this.enemies, this.platforms);

    // Player vs enemies
    this.physics.add.overlap(
      this.player,
      this.enemies,
      this.handlePlayerEnemyCollision,
      undefined,
      this
    );

    // Player vs collectibles
    this.physics.add.overlap(
      this.player,
      this.collectibles,
      this.handlePlayerCollectibleCollision,
      undefined,
      this
    );
  }

  private handleInput() {
    if (!this.player) return;

    const leftPressed = this.cursors.left.isDown || this.wasdKeys.A.isDown;
    const rightPressed = this.cursors.right.isDown || this.wasdKeys.D.isDown;
    const jumpPressed =
      this.cursors.up.isDown ||
      this.wasdKeys.W.isDown ||
      this.wasdKeys.SPACE.isDown;

    this.player.handleInput(leftPressed, rightPressed, jumpPressed);
  }

  private handlePlayerEnemyCollision(player: any, enemy: any) {
    // Damage player and update progress
    const newHealth = Math.max(
      0,
      this.callbacks.playerProgress.currentHealth - enemy.damage
    );

    this.callbacks.setPlayerProgress((prev) => ({
      ...prev,
      currentHealth: newHealth,
    }));

    if (newHealth <= 0) {
      this.callbacks.onGameOver();
    }
  }

  private handlePlayerCollectibleCollision(player: any, collectible: any) {
    collectible.collect();

    // Update player progress based on collectible type
    const currentProgress = this.callbacks.playerProgress;

    this.callbacks.setPlayerProgress((prev) => {
      const newProgress = { ...prev };

      if (collectible.collectibleData.type === "skill") {
        newProgress.skillsUnlocked.push(collectible.collectibleData.name);
        newProgress.experience += 100;
      } else if (collectible.collectibleData.type === "experience") {
        newProgress.experience += collectible.collectibleData.value;
      } else if (collectible.collectibleData.type === "health") {
        newProgress.currentHealth = Math.min(
          newProgress.maxHealth,
          newProgress.currentHealth + 25
        );
      }

      return newProgress;
    });
  }

  private checkLevelCompletion() {
    // Check if all objectives are completed
    const allCollectiblesCollected = this.collectibles.children.entries.every(
      (collectible: any) => collectible.collected
    );

    const allEnemiesDefeated = this.enemies.children.entries.every(
      (enemy: any) => enemy.health <= 0
    );

    if (allCollectiblesCollected && allEnemiesDefeated) {
      // Level completed!
      this.time.delayedCall(1000, () => {
        this.callbacks.setPlayerProgress((prev) => ({
          ...prev,
          level: prev.level + 1,
          experience: prev.experience + 200,
        }));

        // For now, just restart the same level
        this.scene.restart();
      });
    }
  }
}
