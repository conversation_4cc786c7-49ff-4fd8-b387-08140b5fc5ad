import Phaser from "phaser";
import { PlayerProgress } from "../../types/GameTypes";
import Player from "../objects/Player";
import Enemy from "../objects/Enemy";
import Collectible from "../objects/Collectible";

interface GameCallbacks {
  onPause: () => void;
  onGameOver: () => void;
  playerProgress: PlayerProgress;
  setPlayerProgress: React.Dispatch<React.SetStateAction<PlayerProgress>>;
}

export default class GameScene extends Phaser.Scene {
  private player!: Player;
  private platforms!: Phaser.Physics.Arcade.StaticGroup;
  private enemies!: Phaser.Physics.Arcade.Group;
  private collectibles!: Phaser.Physics.Arcade.Group;
  private cursors!: Phaser.Types.Input.Keyboard.CursorKeys;
  private wasdKeys!: any;
  private callbacks: GameCallbacks;
  private currentLevel: any;
  private gameData: any = {};

  constructor(callbacks: GameCallbacks) {
    super({ key: "GameScene" });
    this.callbacks = callbacks;
  }

  create() {
    // Load game data
    this.gameData.skills = this.cache.json.get("skills");
    this.gameData.levels = this.cache.json.get("levels");
    this.gameData.achievements = this.cache.json.get("achievements");
    this.gameData.config = this.cache.json.get("gameConfig");

    // Set up the current level (start with level 1)
    this.currentLevel = this.gameData.levels.levels[0];

    // Create the game world
    this.createWorld();
    this.createPlayer();
    this.createPlatforms();
    this.createEnemies();
    this.createCollectibles();

    // Set up input
    this.setupInput();

    // Set up physics collisions
    this.setupCollisions();

    // Set up camera
    this.cameras.main.setBounds(0, 0, 800, 600);
    this.cameras.main.startFollow(this.player);
  }

  update() {
    if (this.player) {
      this.player.update();
    }

    // Handle input
    this.handleInput();

    // Check for level completion
    this.checkLevelCompletion();
  }

  private createWorld() {
    // Add gradient background
    this.createGradientBackground();

    // Add background elements based on level theme
    this.createBackgroundElements();

    // Add level title
    this.add
      .text(400, 50, this.currentLevel.name, {
        fontSize: "24px",
        color: "#ffffff",
        stroke: "#000000",
        strokeThickness: 2,
      })
      .setOrigin(0.5);
  }

  private createPlayer() {
    this.player = new Player(this, 100, 400);
    this.add.existing(this.player);
    this.physics.add.existing(this.player);
  }

  private createPlatforms() {
    this.platforms = this.physics.add.staticGroup();

    // Create platforms from level data
    this.currentLevel.platforms.forEach((platformData: any) => {
      const platform = this.add.rectangle(
        platformData.x + platformData.width / 2,
        platformData.y + platformData.height / 2,
        platformData.width,
        platformData.height,
        0x8b4513
      );
      this.platforms.add(platform);
    });
  }

  private createEnemies() {
    this.enemies = this.physics.add.group();

    // Create enemies from level data
    this.currentLevel.enemies.forEach((enemyData: any) => {
      const enemy = new Enemy(this, enemyData.x, enemyData.y, enemyData);
      this.enemies.add(enemy);
    });
  }

  private createCollectibles() {
    this.collectibles = this.physics.add.group();

    // Create collectibles from level data
    this.currentLevel.collectibles.forEach((collectibleData: any) => {
      const collectible = new Collectible(
        this,
        collectibleData.x,
        collectibleData.y,
        collectibleData
      );
      this.collectibles.add(collectible);
    });
  }

  private setupInput() {
    this.cursors = this.input.keyboard!.createCursorKeys();
    this.wasdKeys = this.input.keyboard!.addKeys("W,S,A,D,SPACE");

    // Pause key
    this.input.keyboard!.on("keydown-ESC", () => {
      this.callbacks.onPause();
    });
  }

  private setupCollisions() {
    // Player collisions
    this.physics.add.collider(this.player, this.platforms);
    this.physics.add.collider(this.enemies, this.platforms);

    // Player vs enemies
    this.physics.add.overlap(
      this.player,
      this.enemies,
      this.handlePlayerEnemyCollision,
      undefined,
      this
    );

    // Player vs collectibles
    this.physics.add.overlap(
      this.player,
      this.collectibles,
      this.handlePlayerCollectibleCollision,
      undefined,
      this
    );
  }

  private handleInput() {
    if (!this.player) return;

    const leftPressed = this.cursors.left.isDown || this.wasdKeys.A.isDown;
    const rightPressed = this.cursors.right.isDown || this.wasdKeys.D.isDown;
    const jumpPressed =
      this.cursors.up.isDown ||
      this.wasdKeys.W.isDown ||
      this.wasdKeys.SPACE.isDown;

    this.player.handleInput(leftPressed, rightPressed, jumpPressed);
  }

  private handlePlayerEnemyCollision(player: any, enemy: any) {
    // Damage player and update progress
    const newHealth = Math.max(
      0,
      this.callbacks.playerProgress.currentHealth - enemy.damage
    );

    this.callbacks.setPlayerProgress((prev) => ({
      ...prev,
      currentHealth: newHealth,
    }));

    if (newHealth <= 0) {
      this.callbacks.onGameOver();
    }
  }

  private handlePlayerCollectibleCollision(player: any, collectible: any) {
    collectible.collect();

    // Update player progress based on collectible type
    this.callbacks.setPlayerProgress((prev) => {
      const newProgress = { ...prev };

      if (collectible.collectibleData.type === "skill") {
        const skillName = collectible.collectibleData.name;
        newProgress.skillsUnlocked.push(skillName);
        newProgress.experience += 100;

        // Unlock the skill in the player object
        this.player.unlockSkill(collectible.collectibleData.id);

        // Show skill unlock notification
        this.showSkillUnlockNotification(skillName);
      } else if (collectible.collectibleData.type === "experience") {
        newProgress.experience += collectible.collectibleData.value;
      } else if (collectible.collectibleData.type === "health") {
        newProgress.currentHealth = Math.min(
          newProgress.maxHealth,
          newProgress.currentHealth + 25
        );
      } else if (collectible.collectibleData.type === "certificate") {
        newProgress.experience += 150;
        // Add achievement for collecting certificate
        this.showCertificateNotification(collectible.collectibleData.name);
      }

      return newProgress;
    });
  }

  private checkLevelCompletion() {
    // Check if all objectives are completed
    const allCollectiblesCollected = this.collectibles.children.entries.every(
      (collectible: any) => collectible.collected
    );

    const allEnemiesDefeated = this.enemies.children.entries.every(
      (enemy: any) => enemy.health <= 0
    );

    if (allCollectiblesCollected && allEnemiesDefeated) {
      // Level completed!
      this.time.delayedCall(1000, () => {
        this.callbacks.setPlayerProgress((prev) => ({
          ...prev,
          level: prev.level + 1,
          experience: prev.experience + 200,
        }));

        // For now, just restart the same level
        this.scene.restart();
      });
    }
  }

  private createGradientBackground() {
    // Create a simple gradient background
    const graphics = this.add.graphics();

    // Sky (light blue)
    graphics.fillStyle(0x87ceeb);
    graphics.fillRect(0, 0, 800, 300);

    // Horizon (slightly darker)
    graphics.fillStyle(0x70b2cd);
    graphics.fillRect(0, 250, 800, 100);

    // Ground (green)
    graphics.fillStyle(0x228b22);
    graphics.fillRect(0, 350, 800, 250);
  }

  private createBackgroundElements() {
    // Add clouds
    this.createClouds();

    // Add trees and foliage
    this.createTrees();

    // Add decorative elements
    this.createDecorations();
  }

  private createClouds() {
    // Simple cloud shapes
    const cloud1 = this.add.graphics();
    cloud1.fillStyle(0xffffff, 0.8);
    cloud1.fillCircle(0, 0, 30);
    cloud1.fillCircle(25, 0, 35);
    cloud1.fillCircle(50, 0, 30);
    cloud1.fillCircle(25, -15, 25);
    cloud1.setPosition(150, 100);

    const cloud2 = this.add.graphics();
    cloud2.fillStyle(0xffffff, 0.6);
    cloud2.fillCircle(0, 0, 25);
    cloud2.fillCircle(20, 0, 30);
    cloud2.fillCircle(40, 0, 25);
    cloud2.setPosition(600, 80);
  }

  private createTrees() {
    // Background trees
    this.createTree(80, 450, 0.8, 0x228b22);
    this.createTree(720, 420, 1.0, 0x32cd32);
    this.createTree(200, 480, 0.6, 0x228b22);
    this.createTree(650, 460, 0.7, 0x32cd32);
  }

  private createTree(x: number, y: number, scale: number, leafColor: number) {
    // Tree trunk
    const trunk = this.add.rectangle(
      x,
      y + 40 * scale,
      20 * scale,
      80 * scale,
      0x8b4513
    );

    // Tree leaves (multiple circles for fuller look)
    const leaves1 = this.add.circle(x, y, 40 * scale, leafColor);
    const leaves2 = this.add.circle(
      x - 15 * scale,
      y + 10 * scale,
      35 * scale,
      leafColor
    );
    const leaves3 = this.add.circle(
      x + 15 * scale,
      y + 10 * scale,
      35 * scale,
      leafColor
    );

    // Add some depth with darker leaves
    const darkLeaves = this.add.circle(
      x + 5 * scale,
      y + 5 * scale,
      30 * scale,
      leafColor - 0x001100
    );
  }

  private createDecorations() {
    // Add some grass patches
    for (let i = 0; i < 10; i++) {
      const x = Phaser.Math.Between(50, 750);
      const grass = this.add.graphics();
      grass.fillStyle(0x228b22);
      grass.fillRect(x, 520, 3, 15);
      grass.fillRect(x + 3, 525, 3, 10);
      grass.fillRect(x + 6, 522, 3, 13);
    }

    // Add some flowers
    for (let i = 0; i < 5; i++) {
      const x = Phaser.Math.Between(100, 700);
      const flower = this.add.circle(x, 515, 3, 0xff69b4);
      const stem = this.add.rectangle(x, 525, 1, 10, 0x228b22);
    }
  }

  private showSkillUnlockNotification(skillName: string) {
    // Create a prominent notification for skill unlock
    const notification = this.add.container(400, 200);

    // Background with border
    const bg = this.add.graphics();
    bg.fillStyle(0x000000, 0.8);
    bg.fillRect(-150, -50, 300, 100);
    bg.lineStyle(3, 0xffd700);
    bg.strokeRect(-150, -50, 300, 100);

    // Title
    const title = this.add
      .text(0, -20, "SKILL UNLOCKED!", {
        fontSize: "18px",
        color: "#ffd700",
        fontStyle: "bold",
      })
      .setOrigin(0.5);

    // Skill name
    const skillText = this.add
      .text(0, 10, skillName, {
        fontSize: "16px",
        color: "#ffffff",
      })
      .setOrigin(0.5);

    notification.add([bg, title, skillText]);

    // Animation
    notification.setScale(0);
    this.tweens.add({
      targets: notification,
      scaleX: 1,
      scaleY: 1,
      duration: 300,
      ease: "Back.easeOut",
      onComplete: () => {
        this.time.delayedCall(2000, () => {
          this.tweens.add({
            targets: notification,
            alpha: 0,
            duration: 500,
            onComplete: () => notification.destroy(),
          });
        });
      },
    });
  }

  private showCertificateNotification(certName: string) {
    // Create a notification for certificate collection
    const notification = this.add.container(400, 150);

    // Background with border
    const bg = this.add.graphics();
    bg.fillStyle(0x4ecdc4, 0.9);
    bg.fillRect(-175, -40, 350, 80);
    bg.lineStyle(2, 0xffd700);
    bg.strokeRect(-175, -40, 350, 80);

    // Title
    const title = this.add
      .text(0, -15, "CERTIFICATE EARNED!", {
        fontSize: "16px",
        color: "#ffffff",
        fontStyle: "bold",
      })
      .setOrigin(0.5);

    // Certificate name
    const certText = this.add
      .text(0, 10, certName, {
        fontSize: "14px",
        color: "#ffffff",
      })
      .setOrigin(0.5);

    notification.add([bg, title, certText]);

    // Animation
    notification.setAlpha(0);
    this.tweens.add({
      targets: notification,
      alpha: 1,
      duration: 300,
      onComplete: () => {
        this.time.delayedCall(1500, () => {
          this.tweens.add({
            targets: notification,
            alpha: 0,
            y: notification.y - 50,
            duration: 500,
            onComplete: () => notification.destroy(),
          });
        });
      },
    });
  }
}
