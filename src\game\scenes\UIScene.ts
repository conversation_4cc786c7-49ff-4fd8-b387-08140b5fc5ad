import Phaser from 'phaser';

export default class UIScene extends Phaser.Scene {
  private healthText!: Phaser.GameObjects.Text;
  private experienceText!: Phaser.GameObjects.Text;
  private levelText!: Phaser.GameObjects.Text;

  constructor() {
    super({ key: 'UIScene', active: true });
  }

  create() {
    // Create UI elements
    this.createHealthBar();
    this.createExperienceBar();
    this.createLevelDisplay();
    this.createInstructions();
  }

  private createHealthBar() {
    // Health background
    this.add.rectangle(100, 30, 200, 20, 0x000000, 0.5);
    
    // Health bar (will be updated dynamically)
    const healthBar = this.add.rectangle(100, 30, 180, 16, 0xff0000);
    healthBar.setName('healthBar');
    
    // Health text
    this.healthText = this.add.text(20, 20, 'Health: 100/100', {
      fontSize: '16px',
      color: '#ffffff'
    });
  }

  private createExperienceBar() {
    // Experience background
    this.add.rectangle(100, 60, 200, 15, 0x000000, 0.5);
    
    // Experience bar (will be updated dynamically)
    const expBar = this.add.rectangle(100, 60, 0, 11, 0x00ff00);
    expBar.setName('expBar');
    
    // Experience text
    this.experienceText = this.add.text(20, 50, 'XP: 0', {
      fontSize: '14px',
      color: '#ffffff'
    });
  }

  private createLevelDisplay() {
    this.levelText = this.add.text(20, 80, 'Level: 1', {
      fontSize: '16px',
      color: '#ffffff'
    });
  }

  private createInstructions() {
    const instructions = [
      'Arrow Keys / WASD: Move',
      'Space / Up: Jump',
      'ESC: Pause'
    ];
    
    instructions.forEach((instruction, index) => {
      this.add.text(this.cameras.main.width - 20, 20 + (index * 20), instruction, {
        fontSize: '12px',
        color: '#ffffff'
      }).setOrigin(1, 0);
    });
  }

  updateHealth(current: number, max: number) {
    if (this.healthText) {
      this.healthText.setText(`Health: ${current}/${max}`);
      
      const healthBar = this.children.getByName('healthBar') as Phaser.GameObjects.Rectangle;
      if (healthBar) {
        const percentage = current / max;
        healthBar.width = 180 * percentage;
        
        // Change color based on health
        if (percentage > 0.6) {
          healthBar.fillColor = 0x00ff00; // Green
        } else if (percentage > 0.3) {
          healthBar.fillColor = 0xffff00; // Yellow
        } else {
          healthBar.fillColor = 0xff0000; // Red
        }
      }
    }
  }

  updateExperience(current: number) {
    if (this.experienceText) {
      this.experienceText.setText(`XP: ${current}`);
      
      const expBar = this.children.getByName('expBar') as Phaser.GameObjects.Rectangle;
      if (expBar) {
        const percentage = (current % 100) / 100; // Assuming 100 XP per level
        expBar.width = 180 * percentage;
      }
    }
  }

  updateLevel(level: number) {
    if (this.levelText) {
      this.levelText.setText(`Level: ${level}`);
    }
  }
}
