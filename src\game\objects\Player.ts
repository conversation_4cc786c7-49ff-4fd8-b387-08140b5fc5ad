import Phaser from 'phaser';

export default class Player extends Phaser.Physics.Arcade.Sprite {
  private speed: number = 200;
  private jumpForce: number = 400;
  private isGrounded: boolean = false;
  private canDoubleJump: boolean = false;
  private hasDoubleJumped: boolean = false;
  private dashCooldown: number = 0;
  private dashDistance: number = 300;
  private dashDuration: number = 200;
  private isDashing: boolean = false;
  private facing: 'left' | 'right' = 'right';
  
  // Skill flags
  private skills: Set<string> = new Set();

  constructor(scene: Phaser.Scene, x: number, y: number) {
    super(scene, x, y, 'player');
    
    // Set up physics body
    this.setSize(32, 48);
    this.setCollideWorldBounds(true);
    this.setBounce(0.1);
    this.setDragX(800);
    
    // Create visual representation (colored rectangle for now)
    this.setTint(0x4ecdc4); // Teal color for the player
    
    // Initialize animations (placeholder for now)
    this.createAnimations();
  }

  private createAnimations() {
    // For now, we'll just use tint changes to represent different states
    // In a full implementation, you'd create sprite animations here
  }

  update() {
    // Update grounded state
    this.isGrounded = this.body!.touching.down;
    
    // Reset double jump when grounded
    if (this.isGrounded) {
      this.hasDoubleJumped = false;
    }
    
    // Update dash cooldown
    if (this.dashCooldown > 0) {
      this.dashCooldown -= this.scene.game.loop.delta;
    }
    
    // Update visual state
    this.updateVisualState();
  }

  handleInput(leftPressed: boolean, rightPressed: boolean, jumpPressed: boolean) {
    if (this.isDashing) return; // Can't control while dashing
    
    // Horizontal movement
    if (leftPressed) {
      this.setVelocityX(-this.speed);
      this.facing = 'left';
      this.setFlipX(true);
    } else if (rightPressed) {
      this.setVelocityX(this.speed);
      this.facing = 'right';
      this.setFlipX(false);
    }
    
    // Jumping
    if (jumpPressed && this.canJump()) {
      this.jump();
    }
    
    // Dash (if skill is unlocked)
    if (this.skills.has('react') && this.canDash()) {
      // For now, dash is triggered automatically when moving fast
      // In a full implementation, you'd have a separate dash key
      if ((leftPressed || rightPressed) && this.dashCooldown <= 0) {
        this.dash();
      }
    }
  }

  private canJump(): boolean {
    if (this.isGrounded) return true;
    if (this.skills.has('javascript') && !this.hasDoubleJumped) return true;
    return false;
  }

  private jump() {
    this.setVelocityY(-this.jumpForce);
    
    if (!this.isGrounded && this.skills.has('javascript')) {
      this.hasDoubleJumped = true;
      // Visual effect for double jump
      this.setTint(0xffff00); // Yellow flash
      this.scene.time.delayedCall(100, () => {
        this.setTint(0x4ecdc4);
      });
    }
  }

  private canDash(): boolean {
    return this.dashCooldown <= 0 && !this.isDashing;
  }

  private dash() {
    this.isDashing = true;
    this.dashCooldown = 2000; // 2 second cooldown
    
    const dashVelocity = this.facing === 'right' ? this.dashDistance : -this.dashDistance;
    this.setVelocityX(dashVelocity);
    
    // Visual effect for dash
    this.setTint(0xff6b6b); // Red flash
    this.setAlpha(0.7); // Semi-transparent
    
    // End dash after duration
    this.scene.time.delayedCall(this.dashDuration, () => {
      this.isDashing = false;
      this.setTint(0x4ecdc4);
      this.setAlpha(1);
    });
  }

  private updateVisualState() {
    // Change tint based on current state
    if (this.isDashing) return; // Don't change tint while dashing
    
    if (!this.isGrounded) {
      this.setTint(0x87ceeb); // Light blue when in air
    } else if (Math.abs(this.body!.velocity.x) > 10) {
      this.setTint(0x98fb98); // Light green when moving
    } else {
      this.setTint(0x4ecdc4); // Default teal when idle
    }
  }

  // Skill management
  unlockSkill(skillId: string) {
    this.skills.add(skillId);
    
    // Apply skill effects
    switch (skillId) {
      case 'html-css':
        this.speed *= 1.2;
        break;
      case 'javascript':
        this.canDoubleJump = true;
        break;
      case 'react':
        // Dash ability is handled in handleInput
        break;
      case 'nodejs':
        // Health boost would be handled by the game scene
        break;
      case 'docker':
        // Teleport ability (not implemented yet)
        break;
    }
    
    // Visual feedback for skill unlock
    this.setTint(0xffd700); // Gold flash
    this.scene.time.delayedCall(500, () => {
      this.setTint(0x4ecdc4);
    });
  }

  hasSkill(skillId: string): boolean {
    return this.skills.has(skillId);
  }

  getSkills(): string[] {
    return Array.from(this.skills);
  }

  // Damage and health
  takeDamage(amount: number) {
    // Visual feedback for taking damage
    this.setTint(0xff0000); // Red flash
    this.scene.time.delayedCall(200, () => {
      this.setTint(0x4ecdc4);
    });
    
    // Knockback effect
    const knockbackForce = 200;
    const knockbackDirection = this.facing === 'right' ? -1 : 1;
    this.setVelocityX(knockbackForce * knockbackDirection);
  }

  // Teleport ability (for Docker skill)
  teleport(targetX: number, targetY: number) {
    if (!this.skills.has('docker')) return;
    
    // Visual effect
    this.setAlpha(0);
    this.setPosition(targetX, targetY);
    
    this.scene.tweens.add({
      targets: this,
      alpha: 1,
      duration: 300,
      ease: 'Power2'
    });
  }
}
