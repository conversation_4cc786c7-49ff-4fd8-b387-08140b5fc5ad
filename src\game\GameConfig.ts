import Phaser from "phaser";
import { PlayerProgress } from "../types/GameTypes";
import BootScene from "./scenes/BootScene";
import GameScene from "./scenes/GameScene";
import UIScene from "./scenes/UIScene";

interface GameCallbacks {
  onPause: () => void;
  onGameOver: () => void;
  playerProgress: PlayerProgress;
  setPlayerProgress: React.Dispatch<React.SetStateAction<PlayerProgress>>;
}

export const createGameConfig = (
  parent: HTMLElement,
  callbacks: GameCallbacks
): Phaser.Types.Core.GameConfig => {
  return {
    type: Phaser.AUTO,
    width: 800,
    height: 600,
    parent: parent,
    backgroundColor: "#87CEEB",
    physics: {
      default: "arcade",
      arcade: {
        gravity: { x: 0, y: 800 },
        debug: false, // Set to true for debugging physics
      },
    },
    scene: [new BootScene(), new GameScene(callbacks), new UIScene()],
    scale: {
      mode: Phaser.Scale.FIT,
      autoCenter: Phaser.Scale.CENTER_BOTH,
      min: {
        width: 400,
        height: 300,
      },
      max: {
        width: 1200,
        height: 900,
      },
    },
    render: {
      antialias: true,
      pixelArt: false,
    },
    input: {
      keyboard: true,
      mouse: true,
      touch: true,
    },
    audio: {
      disableWebAudio: false,
    },
  };
};
