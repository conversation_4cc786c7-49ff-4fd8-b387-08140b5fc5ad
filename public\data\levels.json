{"levels": [{"id": "level-1", "name": "The Beginning", "description": "Your first steps into the world of programming", "theme": "tutorial", "difficulty": 1, "objectives": ["Learn basic movement", "Collect your first HTML/CSS skill", "Defeat the 'Syntax Error' enemy"], "enemies": [{"id": "syntax-error-1", "name": "Syntax Error", "type": "bug", "health": 20, "damage": 10, "speed": 50, "behavior": "patrol", "sprite": "syntax-error", "x": 400, "y": 300}, {"id": "memory-leak-1", "name": "Memory Leak", "type": "challenge", "health": 30, "damage": 15, "speed": 40, "behavior": "patrol", "sprite": "memory-leak", "x": 600, "y": 350}], "collectibles": [{"id": "html-css-skill", "name": "HTML & CSS Basics", "type": "skill", "value": 1, "sprite": "skill-html-css", "x": 200, "y": 250, "collected": false}, {"id": "experience-1", "name": "First Project Experience", "type": "experience", "value": 50, "sprite": "experience-orb", "x": 600, "y": 200, "collected": false}], "platforms": [{"id": "ground-1", "x": 0, "y": 550, "width": 800, "height": 50, "type": "solid", "sprite": "ground-grass"}, {"id": "platform-1", "x": 150, "y": 400, "width": 100, "height": 20, "type": "jumpThrough", "sprite": "platform-wood"}, {"id": "platform-2", "x": 350, "y": 300, "width": 100, "height": 20, "type": "jumpThrough", "sprite": "platform-wood"}, {"id": "platform-3", "x": 550, "y": 250, "width": 100, "height": 20, "type": "jumpThrough", "sprite": "platform-wood"}], "background": "forest-bg", "music": "tutorial-theme"}, {"id": "level-2", "name": "JavaScript Jungle", "description": "Navigate through the complexities of JavaScript", "theme": "jungle", "difficulty": 2, "objectives": ["Master JavaScript fundamentals", "Unlock double jump ability", "Defeat the 'Undefined Variable' boss"], "enemies": [{"id": "undefined-var-1", "name": "Undefined Variable", "type": "bug", "health": 30, "damage": 15, "speed": 75, "behavior": "chase", "sprite": "undefined-error", "x": 300, "y": 400}, {"id": "null-pointer-1", "name": "<PERSON><PERSON>", "type": "bug", "health": 25, "damage": 12, "speed": 60, "behavior": "patrol", "sprite": "null-error", "x": 500, "y": 350}, {"id": "syntax-error-2", "name": "Syntax Error", "type": "bug", "health": 15, "damage": 8, "speed": 80, "behavior": "chase", "sprite": "syntax-error", "x": 150, "y": 450}], "collectibles": [{"id": "javascript-skill", "name": "JavaScript Mastery", "type": "skill", "value": 1, "sprite": "skill-javascript", "x": 700, "y": 200, "collected": false}, {"id": "certificate-1", "name": "JavaScript Certification", "type": "certificate", "value": 100, "sprite": "certificate-js", "x": 100, "y": 150, "collected": false}], "platforms": [{"id": "ground-2", "x": 0, "y": 550, "width": 800, "height": 50, "type": "solid", "sprite": "ground-jungle"}, {"id": "moving-platform-1", "x": 200, "y": 350, "width": 80, "height": 15, "type": "moving", "sprite": "platform-vine", "movementPattern": {"type": "horizontal", "speed": 50, "distance": 150}}, {"id": "platform-4", "x": 450, "y": 300, "width": 120, "height": 20, "type": "jumpThrough", "sprite": "platform-wood"}], "background": "jungle-bg", "music": "jungle-theme"}, {"id": "level-3", "name": "React Rapids", "description": "Flow through component-based architecture", "theme": "water", "difficulty": 3, "objectives": ["Build your first React component", "Unlock dash ability", "Navigate the Component Lifecycle"], "enemies": [{"id": "infinite-loop-1", "name": "Infinite Loop", "type": "challenge", "health": 40, "damage": 20, "speed": 30, "behavior": "static", "sprite": "infinite-loop", "x": 400, "y": 300}, {"id": "memory-leak-2", "name": "Memory Leak", "type": "challenge", "health": 35, "damage": 18, "speed": 35, "behavior": "patrol", "sprite": "memory-leak", "x": 200, "y": 400}], "collectibles": [{"id": "react-skill", "name": "React Components", "type": "skill", "value": 1, "sprite": "skill-react", "x": 650, "y": 180, "collected": false}], "platforms": [{"id": "ground-3", "x": 0, "y": 550, "width": 800, "height": 50, "type": "solid", "sprite": "ground-water"}], "background": "water-bg", "music": "water-theme"}]}