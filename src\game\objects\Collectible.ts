import Phaser from "phaser";

export default class Collectible extends Phaser.Physics.Arcade.Sprite {
  public collectibleData: any;
  public collected: boolean = false;
  private floatTween?: Phaser.Tweens.Tween;
  private glowTween?: Phaser.Tweens.Tween;

  constructor(scene: Phaser.Scene, x: number, y: number, collectibleData: any) {
    super(scene, x, y, collectibleData.sprite || "experience-orb");

    this.collectibleData = collectibleData;

    // Set up physics
    this.setSize(20, 20);

    // Set color and effects based on collectible type
    this.setAppearanceBasedOnType();

    // Add floating animation
    this.createFloatingAnimation();

    // Add glow effect
    this.createGlowEffect();

    // Add to scene
    scene.add.existing(this);
    scene.physics.add.existing(this);

    // Make it a trigger (no collision, just overlap detection)
    (this.body as Phaser.Physics.Arcade.Body).setImmovable(true);
  }

  private setAppearanceBasedOnType() {
    switch (this.collectibleData.type) {
      case "skill":
        this.setTint(0x4ecdc4); // Teal for skills
        this.setScale(1.2);
        break;
      case "certificate":
        this.setTint(0xffd700); // Gold for certificates
        this.setScale(1.3);
        break;
      case "experience":
        this.setTint(0x4caf50); // Green for experience
        this.setScale(0.8);
        break;
      case "health":
        this.setTint(0xf44336); // Red for health
        this.setScale(1.0);
        break;
      default:
        this.setTint(0xffffff);
    }
  }

  private createFloatingAnimation() {
    // Gentle up and down floating motion
    this.floatTween = this.scene.tweens.add({
      targets: this,
      y: this.y - 10,
      duration: 2000,
      ease: "Sine.easeInOut",
      yoyo: true,
      repeat: -1,
    });
  }

  private createGlowEffect() {
    // Pulsing glow effect
    this.glowTween = this.scene.tweens.add({
      targets: this,
      alpha: 0.6,
      duration: 1500,
      ease: "Sine.easeInOut",
      yoyo: true,
      repeat: -1,
    });
  }

  collect() {
    if (this.collected) return;

    this.collected = true;

    // Stop animations
    if (this.floatTween) {
      this.floatTween.destroy();
    }
    if (this.glowTween) {
      this.glowTween.destroy();
    }

    // Play collection effect
    this.playCollectionEffect();

    // Play sound effect (placeholder)
    // this.scene.sound.play('collect');
  }

  private playCollectionEffect() {
    // Create particle effect
    this.createParticleEffect();

    // Scale up and fade out animation
    this.scene.tweens.add({
      targets: this,
      scaleX: 2,
      scaleY: 2,
      alpha: 0,
      duration: 300,
      ease: "Power2",
      onComplete: () => {
        this.destroy();
      },
    });

    // Show collection text
    this.showCollectionText();
  }

  private createParticleEffect() {
    const particleCount = 8;
    const particles: Phaser.GameObjects.Graphics[] = [];

    for (let i = 0; i < particleCount; i++) {
      const particle = this.scene.add.graphics();
      particle.fillStyle(this.tintTopLeft);
      particle.fillCircle(0, 0, 3);
      particle.setPosition(this.x, this.y);

      const angle = (i / particleCount) * Math.PI * 2;
      const distance = 50;

      this.scene.tweens.add({
        targets: particle,
        x: this.x + Math.cos(angle) * distance,
        y: this.y + Math.sin(angle) * distance,
        alpha: 0,
        duration: 500,
        ease: "Power2",
        onComplete: () => {
          particle.destroy();
        },
      });

      particles.push(particle);
    }
  }

  private showCollectionText() {
    let text = "";
    let color = "#ffffff";

    switch (this.collectibleData.type) {
      case "skill":
        text = `+${this.collectibleData.name}`;
        color = "#4ecdc4";
        break;
      case "certificate":
        text = `Certificate: ${this.collectibleData.name}`;
        color = "#ffd700";
        break;
      case "experience":
        text = `+${this.collectibleData.value} XP`;
        color = "#4caf50";
        break;
      case "health":
        text = "+Health";
        color = "#f44336";
        break;
    }

    const collectText = this.scene.add
      .text(this.x, this.y - 30, text, {
        fontSize: "14px",
        color: color,
        stroke: "#000000",
        strokeThickness: 2,
      })
      .setOrigin(0.5);

    // Animate the text
    this.scene.tweens.add({
      targets: collectText,
      y: collectText.y - 40,
      alpha: 0,
      duration: 1000,
      ease: "Power2",
      onComplete: () => {
        collectText.destroy();
      },
    });
  }

  // Get collectible info for UI display
  getInfo() {
    return {
      name: this.collectibleData.name,
      type: this.collectibleData.type,
      value: this.collectibleData.value,
      description: this.getDescription(),
    };
  }

  private getDescription(): string {
    switch (this.collectibleData.type) {
      case "skill":
        return `Unlock the ${this.collectibleData.name} skill and gain new abilities!`;
      case "certificate":
        return `Earn the ${this.collectibleData.name} certification!`;
      case "experience":
        return `Gain ${this.collectibleData.value} experience points!`;
      case "health":
        return "Restore health points!";
      default:
        return "A mysterious collectible...";
    }
  }

  // Update method for any ongoing effects
  update() {
    if (this.collected) return;

    // Add subtle rotation for visual appeal
    this.rotation += 0.01;

    // Pulse effect based on type
    if (this.collectibleData.type === "skill") {
      const pulse = Math.sin(this.scene.time.now * 0.005) * 0.1 + 1;
      this.setScale(1.2 * pulse);
    }
  }

  // Check if collectible should be auto-collected (for experience orbs from enemies)
  checkAutoCollect(
    playerX: number,
    playerY: number,
    range: number = 50
  ): boolean {
    if (this.collected) return false;

    const distance = Phaser.Math.Distance.Between(
      this.x,
      this.y,
      playerX,
      playerY
    );

    if (distance <= range && this.collectibleData.type === "experience") {
      // Move towards player
      const angle = Phaser.Math.Angle.Between(this.x, this.y, playerX, playerY);
      const speed = 200;

      this.scene.physics.velocityFromAngle(
        Phaser.Math.RadToDeg(angle),
        speed,
        (this.body as Phaser.Physics.Arcade.Body).velocity
      );

      return distance <= 20; // Collect when very close
    }

    return false;
  }
}
