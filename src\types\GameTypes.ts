export type GameState = 'menu' | 'playing' | 'paused' | 'gameOver' | 'achievements';

export interface PlayerProgress {
  level: number;
  experience: number;
  skillsUnlocked: string[];
  achievementsEarned: Achievement[];
  currentHealth: number;
  maxHealth: number;
}

export interface Skill {
  id: string;
  name: string;
  description: string;
  icon: string;
  powerType: 'movement' | 'combat' | 'utility';
  effect: {
    type: 'doubleJump' | 'dash' | 'teleport' | 'shield' | 'speed';
    value: number;
  };
  unlockLevel: number;
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  type: 'skill' | 'level' | 'collectible' | 'challenge';
  requirement: string;
  earned: boolean;
  earnedDate?: Date;
}

export interface Level {
  id: string;
  name: string;
  description: string;
  theme: string;
  difficulty: number;
  objectives: string[];
  enemies: Enemy[];
  collectibles: Collectible[];
  platforms: Platform[];
  background: string;
  music: string;
}

export interface Enemy {
  id: string;
  name: string;
  type: 'bug' | 'challenge' | 'blocker';
  health: number;
  damage: number;
  speed: number;
  behavior: 'patrol' | 'chase' | 'static';
  sprite: string;
  x: number;
  y: number;
}

export interface Collectible {
  id: string;
  name: string;
  type: 'skill' | 'certificate' | 'experience' | 'health';
  value: number;
  sprite: string;
  x: number;
  y: number;
  collected: boolean;
}

export interface Platform {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  type: 'solid' | 'jumpThrough' | 'moving';
  sprite: string;
  movementPattern?: {
    type: 'horizontal' | 'vertical' | 'circular';
    speed: number;
    distance: number;
  };
}

export interface GameConfig {
  width: number;
  height: number;
  gravity: number;
  playerSpeed: number;
  jumpForce: number;
}

export interface PlayerStats {
  position: { x: number; y: number };
  velocity: { x: number; y: number };
  health: number;
  skills: string[];
  facing: 'left' | 'right';
  isGrounded: boolean;
  isJumping: boolean;
  canDoubleJump: boolean;
  dashCooldown: number;
}
