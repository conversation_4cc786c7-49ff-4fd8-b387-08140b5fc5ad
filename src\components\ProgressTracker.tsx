import React from 'react';
import styled from 'styled-components';
import { PlayerProgress } from '../types/GameTypes';

const TrackerContainer = styled.div`
  position: fixed;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 15px;
  border-radius: 10px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 200px;
  pointer-events: auto;
`;

const ProgressItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 0.9rem;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const ProgressLabel = styled.span`
  opacity: 0.8;
`;

const ProgressValue = styled.span`
  font-weight: bold;
  color: #4ecdc4;
`;

const HealthBar = styled.div`
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
  margin-top: 5px;
`;

const HealthFill = styled.div<{ percentage: number }>`
  width: ${props => props.percentage}%;
  height: 100%;
  background: linear-gradient(90deg, #ff6b6b, #ee5a24);
  transition: width 0.3s ease;
`;

const ExperienceBar = styled.div`
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
  margin-top: 5px;
`;

const ExperienceFill = styled.div<{ percentage: number }>`
  width: ${props => props.percentage}%;
  height: 100%;
  background: linear-gradient(90deg, #4ecdc4, #44a08d);
  transition: width 0.3s ease;
`;

const SkillsList = styled.div`
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
`;

const SkillItem = styled.div`
  font-size: 0.8rem;
  color: #4ecdc4;
  margin-bottom: 3px;
  display: flex;
  align-items: center;
  
  &:before {
    content: '⚡';
    margin-right: 5px;
  }
`;

interface ProgressTrackerProps {
  progress: PlayerProgress;
}

const ProgressTracker: React.FC<ProgressTrackerProps> = ({ progress }) => {
  const healthPercentage = (progress.currentHealth / progress.maxHealth) * 100;
  const experiencePercentage = (progress.experience % 100); // Assuming 100 XP per level
  
  return (
    <TrackerContainer>
      <ProgressItem>
        <ProgressLabel>Level:</ProgressLabel>
        <ProgressValue>{progress.level}</ProgressValue>
      </ProgressItem>
      
      <ProgressItem>
        <ProgressLabel>Health:</ProgressLabel>
        <ProgressValue>{progress.currentHealth}/{progress.maxHealth}</ProgressValue>
      </ProgressItem>
      <HealthBar>
        <HealthFill percentage={healthPercentage} />
      </HealthBar>
      
      <ProgressItem>
        <ProgressLabel>Experience:</ProgressLabel>
        <ProgressValue>{progress.experience} XP</ProgressValue>
      </ProgressItem>
      <ExperienceBar>
        <ExperienceFill percentage={experiencePercentage} />
      </ExperienceBar>
      
      <ProgressItem>
        <ProgressLabel>Skills:</ProgressLabel>
        <ProgressValue>{progress.skillsUnlocked.length}</ProgressValue>
      </ProgressItem>
      
      {progress.skillsUnlocked.length > 0 && (
        <SkillsList>
          {progress.skillsUnlocked.slice(-3).map((skill, index) => (
            <SkillItem key={index}>{skill}</SkillItem>
          ))}
          {progress.skillsUnlocked.length > 3 && (
            <SkillItem>...and {progress.skillsUnlocked.length - 3} more</SkillItem>
          )}
        </SkillsList>
      )}
    </TrackerContainer>
  );
};

export default ProgressTracker;
