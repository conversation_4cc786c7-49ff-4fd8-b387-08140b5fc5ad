import React, { useEffect, useRef } from "react";
import styled from "styled-components";
import Phaser from "phaser";
import { GameState, PlayerProgress } from "../types/GameTypes";
import { createGameConfig } from "../game/GameConfig";

const GameWrapper = styled.div`
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #000;
`;

const GameCanvas = styled.div`
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
`;

const PauseOverlay = styled.div<{ visible: boolean }>`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: ${(props) => (props.visible ? "flex" : "none")};
  justify-content: center;
  align-items: center;
  flex-direction: column;
  color: white;
  z-index: 2000;
`;

const PauseTitle = styled.h2`
  font-size: 2rem;
  margin-bottom: 2rem;
`;

const PauseButton = styled.button`
  background: linear-gradient(45deg, #4ecdc4, #44a08d);
  border: none;
  color: white;
  padding: 15px 30px;
  font-size: 1.1rem;
  border-radius: 25px;
  cursor: pointer;
  margin: 10px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  }
`;

interface GameContainerProps {
  gameState: GameState;
  onPause: () => void;
  onGameOver: () => void;
  playerProgress: PlayerProgress;
  setPlayerProgress: React.Dispatch<React.SetStateAction<PlayerProgress>>;
}

const GameContainer: React.FC<GameContainerProps> = ({
  gameState,
  onPause,
  onGameOver,
  playerProgress,
  setPlayerProgress,
}) => {
  const gameRef = useRef<HTMLDivElement>(null);
  const phaserGameRef = useRef<Phaser.Game | null>(null);

  useEffect(() => {
    if (gameRef.current && !phaserGameRef.current) {
      // Create the Phaser game configuration
      const config = createGameConfig(gameRef.current, {
        onPause,
        onGameOver,
        playerProgress,
        setPlayerProgress,
      });

      // Initialize the Phaser game
      phaserGameRef.current = new Phaser.Game(config);
    }

    return () => {
      // Cleanup when component unmounts
      if (phaserGameRef.current) {
        phaserGameRef.current.destroy(true);
        phaserGameRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    // Handle game state changes
    if (phaserGameRef.current) {
      if (gameState === "paused") {
        phaserGameRef.current.scene.pause("GameScene");
      } else if (gameState === "playing") {
        phaserGameRef.current.scene.resume("GameScene");
      }
    }
  }, [gameState]);

  const handleResume = () => {
    if (phaserGameRef.current) {
      phaserGameRef.current.scene.resume("GameScene");
    }
  };

  const handleRestart = () => {
    if (phaserGameRef.current) {
      const gameScene = phaserGameRef.current.scene.getScene("GameScene");
      if (gameScene) {
        gameScene.scene.restart();
      }
    }
  };

  return (
    <GameWrapper>
      <GameCanvas ref={gameRef} />

      <PauseOverlay visible={gameState === "paused"}>
        <PauseTitle>Game Paused</PauseTitle>
        <div>
          <PauseButton onClick={handleResume}>Resume</PauseButton>
          <PauseButton onClick={handleRestart}>Restart Level</PauseButton>
          <PauseButton onClick={onGameOver}>Main Menu</PauseButton>
        </div>
      </PauseOverlay>
    </GameWrapper>
  );
};

export default GameContainer;
