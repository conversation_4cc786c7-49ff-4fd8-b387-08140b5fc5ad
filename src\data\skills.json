{"skills": [{"id": "html-css", "name": "HTML & CSS", "description": "Foundation of web development - gives you basic movement", "icon": "🏗️", "powerType": "movement", "effect": {"type": "speed", "value": 1.2}, "unlockLevel": 1}, {"id": "javascript", "name": "JavaScript", "description": "Dynamic programming language - unlocks double jump", "icon": "⚡", "powerType": "movement", "effect": {"type": "doubleJump", "value": 1}, "unlockLevel": 2}, {"id": "react", "name": "React", "description": "Component-based UI library - enables dash ability", "icon": "⚛️", "powerType": "movement", "effect": {"type": "dash", "value": 300}, "unlockLevel": 3}, {"id": "nodejs", "name": "Node.js", "description": "Server-side JavaScript - increases health", "icon": "🟢", "powerType": "utility", "effect": {"type": "shield", "value": 50}, "unlockLevel": 4}, {"id": "typescript", "name": "TypeScript", "description": "Typed JavaScript - provides better code structure and debugging", "icon": "🔷", "powerType": "utility", "effect": {"type": "shield", "value": 25}, "unlockLevel": 5}, {"id": "git", "name": "Git & Version Control", "description": "Track changes and collaborate - enables save points", "icon": "📝", "powerType": "utility", "effect": {"type": "shield", "value": 30}, "unlockLevel": 2}, {"id": "docker", "name": "<PERSON>er", "description": "Containerization technology - unlocks teleport ability", "icon": "🐳", "powerType": "movement", "effect": {"type": "teleport", "value": 200}, "unlockLevel": 6}, {"id": "aws", "name": "AWS Cloud", "description": "Cloud computing platform - provides cloud jump", "icon": "☁️", "powerType": "movement", "effect": {"type": "doubleJump", "value": 2}, "unlockLevel": 7}, {"id": "database", "name": "Database Management", "description": "Data storage and retrieval - increases experience gain", "icon": "🗄️", "powerType": "utility", "effect": {"type": "speed", "value": 1.5}, "unlockLevel": 4}, {"id": "testing", "name": "Testing & QA", "description": "Ensure code quality - provides bug detection", "icon": "🧪", "powerType": "combat", "effect": {"type": "shield", "value": 40}, "unlockLevel": 5}]}