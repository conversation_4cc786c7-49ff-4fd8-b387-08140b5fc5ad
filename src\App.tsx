import React, { useState } from "react";
import styled from "styled-components";
import GameContainer from "./components/GameContainer";
import MainMenu from "./components/MainMenu";
import ProgressTracker from "./components/ProgressTracker";
import AchievementList from "./components/AchievementList";
import { GameState, PlayerProgress } from "./types/GameTypes";

const AppContainer = styled.div`
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
`;

const UIOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
`;

const App: React.FC = () => {
  const [gameState, setGameState] = useState<GameState>("menu");
  const [playerProgress, setPlayerProgress] = useState<PlayerProgress>({
    level: 1,
    experience: 0,
    skillsUnlocked: [],
    achievementsEarned: [],
    currentHealth: 100,
    maxHealth: 100,
  });

  const handleStartGame = () => {
    setGameState("playing");
  };

  const handlePauseGame = () => {
    setGameState("paused");
  };

  const handleGameOver = () => {
    setGameState("gameOver");
  };

  const handleReturnToMenu = () => {
    setGameState("menu");
  };

  return (
    <AppContainer>
      {gameState === "menu" && <MainMenu onStartGame={handleStartGame} />}

      {(gameState === "playing" || gameState === "paused") && (
        <>
          <GameContainer
            gameState={gameState}
            onPause={handlePauseGame}
            onGameOver={handleGameOver}
            playerProgress={playerProgress}
            setPlayerProgress={setPlayerProgress}
          />
          <UIOverlay>
            <ProgressTracker progress={playerProgress} />
          </UIOverlay>
        </>
      )}

      {gameState === "achievements" && (
        <AchievementList
          achievements={playerProgress.achievementsEarned}
          onBack={handleReturnToMenu}
        />
      )}
    </AppContainer>
  );
};

export default App;
