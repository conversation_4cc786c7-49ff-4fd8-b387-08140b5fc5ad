import React from 'react';
import styled from 'styled-components';
import { Achievement } from '../types/GameTypes';

const AchievementContainer = styled.div`
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px;
  overflow-y: auto;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 40px;
`;

const Title = styled.h1`
  font-size: 3rem;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
`;

const BackButton = styled.button`
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  border: none;
  color: white;
  padding: 10px 20px;
  font-size: 1rem;
  border-radius: 25px;
  cursor: pointer;
  margin-bottom: 20px;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }
`;

const AchievementGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
`;

const AchievementCard = styled.div<{ earned: boolean }>`
  background: ${props => props.earned 
    ? 'linear-gradient(135deg, #4ecdc4, #44a08d)' 
    : 'rgba(255, 255, 255, 0.1)'};
  padding: 20px;
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  opacity: ${props => props.earned ? 1 : 0.6};
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  }
`;

const AchievementIcon = styled.div`
  font-size: 2rem;
  margin-bottom: 10px;
`;

const AchievementName = styled.h3`
  margin: 0 0 10px 0;
  font-size: 1.2rem;
`;

const AchievementDescription = styled.p`
  margin: 0 0 10px 0;
  opacity: 0.8;
  font-size: 0.9rem;
  line-height: 1.4;
`;

const AchievementType = styled.span`
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  text-transform: uppercase;
`;

const EarnedDate = styled.div`
  margin-top: 10px;
  font-size: 0.8rem;
  opacity: 0.7;
`;

interface AchievementListProps {
  achievements: Achievement[];
  onBack: () => void;
}

// Sample achievements for demonstration
const sampleAchievements: Achievement[] = [
  {
    id: '1',
    name: 'First Steps',
    description: 'Complete your first level and begin the journey',
    icon: '👶',
    type: 'level',
    requirement: 'Complete Level 1',
    earned: true,
    earnedDate: new Date('2024-01-15')
  },
  {
    id: '2',
    name: 'JavaScript Master',
    description: 'Unlock the power of JavaScript and gain double jump ability',
    icon: '⚡',
    type: 'skill',
    requirement: 'Learn JavaScript fundamentals',
    earned: true,
    earnedDate: new Date('2024-02-01')
  },
  {
    id: '3',
    name: 'React Ninja',
    description: 'Master React and unlock the dash ability',
    icon: '🥷',
    type: 'skill',
    requirement: 'Build 3 React projects',
    earned: false
  },
  {
    id: '4',
    name: 'Bug Squasher',
    description: 'Defeat 50 bug enemies throughout your journey',
    icon: '🐛',
    type: 'challenge',
    requirement: 'Defeat 50 bugs',
    earned: false
  }
];

const AchievementList: React.FC<AchievementListProps> = ({ achievements, onBack }) => {
  const displayAchievements = achievements.length > 0 ? achievements : sampleAchievements;
  
  return (
    <AchievementContainer>
      <Header>
        <BackButton onClick={onBack}>← Back to Menu</BackButton>
        <Title>🏆 Achievements</Title>
      </Header>
      
      <AchievementGrid>
        {displayAchievements.map((achievement) => (
          <AchievementCard key={achievement.id} earned={achievement.earned}>
            <AchievementIcon>{achievement.icon}</AchievementIcon>
            <AchievementName>{achievement.name}</AchievementName>
            <AchievementDescription>{achievement.description}</AchievementDescription>
            <AchievementType>{achievement.type}</AchievementType>
            {achievement.earned && achievement.earnedDate && (
              <EarnedDate>
                Earned: {achievement.earnedDate.toLocaleDateString()}
              </EarnedDate>
            )}
          </AchievementCard>
        ))}
      </AchievementGrid>
    </AchievementContainer>
  );
};

export default AchievementList;
