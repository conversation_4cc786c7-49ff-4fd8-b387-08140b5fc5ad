import React from 'react';
import styled, { keyframes } from 'styled-components';

const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const pulse = keyframes`
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
`;

const MenuContainer = styled.div`
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
  animation: ${fadeIn} 1s ease-out;
`;

const Title = styled.h1`
  font-size: 4rem;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  background: linear-gradient(45deg, #fff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
`;

const Subtitle = styled.h2`
  font-size: 1.5rem;
  margin-bottom: 3rem;
  opacity: 0.9;
  font-weight: 300;
`;

const MenuButton = styled.button`
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  border: none;
  color: white;
  padding: 15px 30px;
  font-size: 1.2rem;
  font-weight: bold;
  border-radius: 50px;
  cursor: pointer;
  margin: 10px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    animation: ${pulse} 1s infinite;
  }
  
  &:active {
    transform: translateY(0);
  }
`;

const GameDescription = styled.p`
  max-width: 600px;
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  opacity: 0.8;
`;

const FeatureList = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
  margin-bottom: 2rem;
`;

const Feature = styled.div`
  background: rgba(255, 255, 255, 0.1);
  padding: 10px 20px;
  border-radius: 25px;
  font-size: 0.9rem;
  backdrop-filter: blur(10px);
`;

interface MainMenuProps {
  onStartGame: () => void;
}

const MainMenu: React.FC<MainMenuProps> = ({ onStartGame }) => {
  return (
    <MenuContainer>
      <Title>🎮 My Developer Journey</Title>
      <Subtitle>A 2D Platformer Portfolio</Subtitle>
      
      <GameDescription>
        Experience my coding journey as an interactive adventure! Jump through levels 
        representing my growth, collect skills as power-ups, and overcome challenges 
        that mirror real development obstacles.
      </GameDescription>
      
      <FeatureList>
        <Feature>🚀 Skills = Power-ups</Feature>
        <Feature>📜 Certificates = Collectibles</Feature>
        <Feature>🐛 Bugs = Enemies</Feature>
        <Feature>🏗️ Projects = Levels</Feature>
      </FeatureList>
      
      <div>
        <MenuButton onClick={onStartGame}>
          Start Journey
        </MenuButton>
        <MenuButton onClick={() => alert('Coming Soon!')}>
          View Achievements
        </MenuButton>
        <MenuButton onClick={() => alert('Coming Soon!')}>
          Settings
        </MenuButton>
      </div>
    </MenuContainer>
  );
};

export default MainMenu;
