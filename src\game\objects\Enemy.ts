import Phaser from "phaser";

export default class Enemy extends Phaser.Physics.Arcade.Sprite {
  public health: number;
  public maxHealth: number;
  public damage: number;
  public speed: number;
  public behavior: "patrol" | "chase" | "static";
  public enemyData: any;

  private patrolDistance: number = 100;
  private patrolStartX: number;
  private patrolDirection: number = 1;
  private chaseRange: number = 150;
  private attackCooldown: number = 0;
  private isAlive: boolean = true;

  constructor(scene: Phaser.Scene, x: number, y: number, enemyData: any) {
    super(scene, x, y, enemyData.sprite || "syntax-error");

    this.enemyData = enemyData;
    this.health = enemyData.health;
    this.maxHealth = enemyData.health;
    this.damage = enemyData.damage;
    this.speed = enemyData.speed;
    this.behavior = enemyData.behavior;

    this.patrolStartX = x;

    // Set up physics
    this.setSize(24, 32);
    this.setCollideWorldBounds(true);
    this.setBounce(0.1);

    // Set color based on enemy type
    this.setTintBasedOnType();

    // Add to scene
    scene.add.existing(this);
    scene.physics.add.existing(this);
  }

  private setTintBasedOnType() {
    switch (this.enemyData.type) {
      case "bug":
        this.setTint(0xff6b6b); // Red for bugs
        break;
      case "challenge":
        this.setTint(0xffa726); // Orange for challenges
        break;
      case "blocker":
        this.setTint(0xab47bc); // Purple for blockers
        break;
      default:
        this.setTint(0xff6b6b);
    }
  }

  update() {
    if (!this.isAlive) return;

    // Update attack cooldown
    if (this.attackCooldown > 0) {
      this.attackCooldown -= this.scene.game.loop.delta;
    }

    // Execute behavior
    switch (this.behavior) {
      case "patrol":
        this.patrolBehavior();
        break;
      case "chase":
        this.chaseBehavior();
        break;
      case "static":
        this.staticBehavior();
        break;
    }

    // Update visual state
    this.updateVisualState();
  }

  private patrolBehavior() {
    // Move back and forth within patrol distance
    const distanceFromStart = Math.abs(this.x - this.patrolStartX);

    if (distanceFromStart >= this.patrolDistance) {
      this.patrolDirection *= -1; // Reverse direction
    }

    this.setVelocityX(this.speed * this.patrolDirection);
    this.setFlipX(this.patrolDirection < 0);
  }

  private chaseBehavior() {
    // Find the player by searching through all game objects
    const allObjects = this.scene.children.list;
    const player = allObjects.find(
      (obj: any) => obj.texture && obj.texture.key === "player"
    );

    if (player) {
      const playerX = (player as any).x;
      const distance = Math.abs(this.x - playerX);

      if (distance <= this.chaseRange) {
        // Chase the player
        const direction = playerX > this.x ? 1 : -1;
        this.setVelocityX(this.speed * direction);
        this.setFlipX(direction < 0);

        // Change tint to show aggressive state
        this.setTint(0xff0000);
      } else {
        // Return to patrol behavior when player is out of range
        this.setVelocityX(0);
        this.setTintBasedOnType();
      }
    }
  }

  private staticBehavior() {
    // Don't move, just stay in place
    this.setVelocityX(0);
  }

  private updateVisualState() {
    // Flash when damaged
    if (this.health < this.maxHealth) {
      const healthPercentage = this.health / this.maxHealth;
      if (healthPercentage < 0.3) {
        // Flash red when low health
        const flashIntensity = Math.sin(this.scene.time.now * 0.01) * 0.5 + 0.5;
        this.setAlpha(0.5 + flashIntensity * 0.5);
      }
    }
  }

  takeDamage(amount: number) {
    if (!this.isAlive) return;

    this.health -= amount;

    // Visual feedback
    this.setTint(0xffffff); // White flash
    this.scene.time.delayedCall(100, () => {
      this.setTintBasedOnType();
    });

    // Knockback effect
    const knockbackForce = 150;
    const knockbackDirection = Math.random() > 0.5 ? 1 : -1;
    this.setVelocityX(knockbackForce * knockbackDirection);

    if (this.health <= 0) {
      this.die();
    }
  }

  private die() {
    this.isAlive = false;

    // Death animation (fade out and shrink)
    this.scene.tweens.add({
      targets: this,
      alpha: 0,
      scaleX: 0.1,
      scaleY: 0.1,
      duration: 500,
      ease: "Power2",
      onComplete: () => {
        this.destroy();
      },
    });

    // Spawn experience orb or other rewards
    this.spawnRewards();
  }

  private spawnRewards() {
    // Create a simple experience orb
    const expOrb = this.scene.add.circle(this.x, this.y, 8, 0x4caf50);
    this.scene.physics.add.existing(expOrb);

    // Make it bounce
    (expOrb.body as Phaser.Physics.Arcade.Body).setBounce(0.8);
    (expOrb.body as Phaser.Physics.Arcade.Body).setVelocity(
      Phaser.Math.Between(-100, 100),
      Phaser.Math.Between(-200, -100)
    );

    // Auto-collect after a few seconds
    this.scene.time.delayedCall(3000, () => {
      if (expOrb.active) {
        expOrb.destroy();
      }
    });
  }

  // Check if enemy can attack
  canAttack(): boolean {
    return this.attackCooldown <= 0 && this.isAlive;
  }

  // Perform attack
  attack() {
    if (!this.canAttack()) return;

    this.attackCooldown = 1000; // 1 second cooldown

    // Visual attack effect
    this.setScale(1.2);
    this.scene.time.delayedCall(200, () => {
      this.setScale(1);
    });

    return this.damage;
  }

  // Get enemy info for UI display
  getInfo() {
    return {
      name: this.enemyData.name,
      type: this.enemyData.type,
      health: this.health,
      maxHealth: this.maxHealth,
      damage: this.damage,
    };
  }
}
