{"achievements": [{"id": "first-steps", "name": "First Steps", "description": "Complete your first level and begin the journey", "icon": "👶", "type": "level", "requirement": "Complete Level 1", "earned": false}, {"id": "html-master", "name": "HTML Master", "description": "Learn the structure of the web", "icon": "🏗️", "type": "skill", "requirement": "Collect HTML & CSS skill", "earned": false}, {"id": "javascript-ninja", "name": "JavaScript Ninja", "description": "Master the language of the web and unlock double jump", "icon": "⚡", "type": "skill", "requirement": "Collect JavaScript skill", "earned": false}, {"id": "react-wizard", "name": "React Wizard", "description": "Component mastery grants you the dash ability", "icon": "⚛️", "type": "skill", "requirement": "Collect React skill", "earned": false}, {"id": "bug-squasher", "name": "<PERSON><PERSON>", "description": "Defeat 10 bug enemies throughout your journey", "icon": "🐛", "type": "challenge", "requirement": "Defeat 10 bugs", "earned": false}, {"id": "syntax-slayer", "name": "Syntax Slayer", "description": "Overcome your first syntax error", "icon": "⚔️", "type": "challenge", "requirement": "Defeat Syntax Error enemy", "earned": false}, {"id": "certificate-collector", "name": "Certificate Collector", "description": "Earn your first certification", "icon": "📜", "type": "collectible", "requirement": "Collect 1 certificate", "earned": false}, {"id": "experience-hunter", "name": "Experience Hunter", "description": "Gain 500 experience points", "icon": "🎯", "type": "collectible", "requirement": "Gain 500 XP", "earned": false}, {"id": "level-explorer", "name": "Level Explorer", "description": "Complete 3 different levels", "icon": "🗺️", "type": "level", "requirement": "Complete 3 levels", "earned": false}, {"id": "skill-collector", "name": "Skill Collector", "description": "Unlock 5 different programming skills", "icon": "🎒", "type": "skill", "requirement": "Unlock 5 skills", "earned": false}, {"id": "full-stack-hero", "name": "Full Stack Hero", "description": "Master both frontend and backend technologies", "icon": "🦸", "type": "skill", "requirement": "Unlock HTML/CSS, JavaScript, React, and Node.js", "earned": false}, {"id": "cloud-walker", "name": "<PERSON>", "description": "Ascend to the cloud with AWS mastery", "icon": "☁️", "type": "skill", "requirement": "Unlock AWS skill", "earned": false}, {"id": "container-captain", "name": "Container Captain", "description": "Master containerization with Docker", "icon": "🐳", "type": "skill", "requirement": "Unlock Docker skill", "earned": false}, {"id": "testing-champion", "name": "Testing Champion", "description": "Ensure quality through comprehensive testing", "icon": "🧪", "type": "skill", "requirement": "Unlock Testing skill", "earned": false}, {"id": "database-guardian", "name": "Database Guardian", "description": "Protect and manage data with database skills", "icon": "🗄️", "type": "skill", "requirement": "Unlock Database skill", "earned": false}, {"id": "git-master", "name": "Git Master", "description": "Version control mastery for collaborative development", "icon": "📝", "type": "skill", "requirement": "Unlock Git skill", "earned": false}, {"id": "typescript-titan", "name": "TypeScript Titan", "description": "Embrace type safety and better code structure", "icon": "🔷", "type": "skill", "requirement": "Unlock TypeScript skill", "earned": false}, {"id": "perfectionist", "name": "Perfectionist", "description": "Complete a level without taking any damage", "icon": "💎", "type": "challenge", "requirement": "Complete level with full health", "earned": false}, {"id": "speed-runner", "name": "Speed Runner", "description": "Complete Level 1 in under 2 minutes", "icon": "💨", "type": "challenge", "requirement": "Complete Level 1 in under 2 minutes", "earned": false}, {"id": "city-of-code", "name": "City of Code", "description": "Reach the final level and become a true developer", "icon": "🏙️", "type": "level", "requirement": "Complete the final level", "earned": false}]}