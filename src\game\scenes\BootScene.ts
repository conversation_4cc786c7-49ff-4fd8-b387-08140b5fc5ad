import Phaser from "phaser";

export default class BootScene extends Phaser.Scene {
  constructor() {
    super({ key: "BootScene" });
  }

  preload() {
    // Create loading bar
    this.createLoadingBar();

    // Load placeholder assets (we'll create simple colored rectangles for now)
    this.loadPlaceholderAssets();

    // Load game data
    this.load.json("skills", "data/skills.json");
    this.load.json("levels", "data/levels.json");
    this.load.json("achievements", "data/achievements.json");
    this.load.json("gameConfig", "data/gameConfig.json");
  }

  create() {
    // Create placeholder sprites programmatically
    this.createPlaceholderSprites();

    // Start the main game scene
    this.scene.start("GameScene");
  }

  private createLoadingBar() {
    const width = this.cameras.main.width;
    const height = this.cameras.main.height;

    // Loading bar background
    const progressBg = this.add.graphics();
    progressBg.fillStyle(0x222222);
    progressBg.fillRect(width / 4, height / 2 - 16, width / 2, 32);

    // Loading bar fill
    const progressBar = this.add.graphics();

    // Loading text
    const loadingText = this.add
      .text(width / 2, height / 2 - 50, "Loading...", {
        fontSize: "20px",
        color: "#ffffff",
      })
      .setOrigin(0.5);

    // Update loading bar
    this.load.on("progress", (value: number) => {
      progressBar.clear();
      progressBar.fillStyle(0x4ecdc4);
      progressBar.fillRect(width / 4, height / 2 - 16, (width / 2) * value, 32);
    });

    // Complete loading
    this.load.on("complete", () => {
      progressBar.destroy();
      progressBg.destroy();
      loadingText.destroy();
    });
  }

  private loadPlaceholderAssets() {
    // We'll create the sprites programmatically in the create method
    // This is more reliable than using data URLs
  }

  private createPlaceholderSprites() {
    const graphics = this.add.graphics();

    // Player sprite (teal rectangle)
    graphics.fillStyle(0x4ecdc4);
    graphics.fillRect(0, 0, 32, 48);
    graphics.generateTexture("player", 32, 48);

    // Platform sprites
    graphics.clear();
    graphics.fillStyle(0x8b4513);
    graphics.fillRect(0, 0, 100, 20);
    graphics.generateTexture("platform-wood", 100, 20);

    graphics.clear();
    graphics.fillStyle(0x228b22);
    graphics.fillRect(0, 0, 100, 50);
    graphics.generateTexture("ground-grass", 100, 50);

    // Enemy sprites
    graphics.clear();
    graphics.fillStyle(0xff6b6b);
    graphics.fillRect(0, 0, 24, 32);
    graphics.generateTexture("syntax-error", 24, 32);

    graphics.clear();
    graphics.fillStyle(0xffa726);
    graphics.fillRect(0, 0, 24, 32);
    graphics.generateTexture("undefined-error", 24, 32);

    // Collectible sprites
    graphics.clear();
    graphics.fillStyle(0x4ecdc4);
    graphics.fillCircle(10, 10, 10);
    graphics.generateTexture("skill-html-css", 20, 20);

    graphics.clear();
    graphics.fillStyle(0xffff00);
    graphics.fillCircle(10, 10, 10);
    graphics.generateTexture("skill-javascript", 20, 20);

    graphics.clear();
    graphics.fillStyle(0x4caf50);
    graphics.fillCircle(8, 8, 8);
    graphics.generateTexture("experience-orb", 16, 16);

    graphics.destroy();
  }
}
